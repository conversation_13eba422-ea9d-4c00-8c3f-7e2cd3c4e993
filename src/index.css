@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 218 23% 97%;
    --foreground: 220 8.9% 11%;

    --card: 0 0% 100%;
    --card-foreground: 220 8.9% 11%;

    --popover: 0 0% 100%;
    --popover-foreground: 220 8.9% 11%;

    --primary: 221 39% 11%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 220 8.9% 11%;

    --muted: 220 14.3% 95.9%;
    --muted-foreground: 220 8.9% 46.1%;

    --accent: 220 14.3% 95.9%;
    --accent-foreground: 220 8.9% 11%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 221 39% 11%;

    --radius: 0.75rem;

    /* Chat specific colors */
    --chat-background: 218 23% 97%;
    --chat-sidebar: 220 14.3% 95.9%;
    --chat-message-user: 221 39% 11%;
    --chat-message-user-foreground: 210 40% 98%;
    --chat-message-ai: 0 0% 100%;
    --chat-message-ai-foreground: 220 8.9% 11%;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(221 39% 11%), hsl(221 39% 20%));
    --gradient-chat: linear-gradient(180deg, hsl(218 23% 97%), hsl(220 14.3% 95.9%));
    --gradient-message: linear-gradient(135deg, hsl(0 0% 100%), hsl(220 14.3% 98%));

    /* Animations */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-spring: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;

    /* Dark mode chat colors */
    --chat-background: 222.2 84% 4.9%;
    --chat-sidebar: 217.2 32.6% 17.5%;
    --chat-message-user: 210 40% 98%;
    --chat-message-user-foreground: 222.2 47.4% 11.2%;
    --chat-message-ai: 217.2 32.6% 17.5%;
    --chat-message-ai-foreground: 210 40% 98%;
    
    /* Dark gradients */
    --gradient-primary: linear-gradient(135deg, hsl(210 40% 98%), hsl(217.2 32.6% 85%));
    --gradient-chat: linear-gradient(180deg, hsl(222.2 84% 4.9%), hsl(217.2 32.6% 17.5%));
    --gradient-message: linear-gradient(135deg, hsl(217.2 32.6% 17.5%), hsl(217.2 32.6% 20%));
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}