import { useState, useRef, useEffect } from 'react';
import { pipeline } from '@huggingface/transformers';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Card } from '@/components/ui/card';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Send, Bot, User, Loader2, AlertTriangle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { validateChatInput, checkRateLimit } from '@/lib/security';
import { useToast } from '@/hooks/use-toast';

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

export const ChatInterface = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isModelLoading, setIsModelLoading] = useState(false);
  const [generator, setGenerator] = useState<any>(null);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();

  useEffect(() => {
    // Initialize the model
    const initModel = async () => {
      setIsModelLoading(true);

      // List of models to try in order of preference
      const modelsToTry = [
        { name: 'onnx-community/Llama-3.2-1B-Instruct', device: 'webgpu' },
        { name: 'onnx-community/Llama-3.2-1B-Instruct', device: 'wasm' },
        { name: 'Xenova/gpt2', device: 'wasm' },
        { name: 'Xenova/distilgpt2', device: 'wasm' }
      ];

      for (const modelConfig of modelsToTry) {
        try {
          console.log(`Attempting to load ${modelConfig.name} on ${modelConfig.device}...`);

          const pipe = await pipeline(
            'text-generation',
            modelConfig.name,
            {
              device: modelConfig.device,
              progress_callback: (progress: any) => {
                console.log('Model loading progress:', progress);
              }
            }
          );

          setGenerator(pipe);
          console.log(`Successfully loaded ${modelConfig.name} on ${modelConfig.device}`);

          toast({
            title: "Model Loaded Successfully",
            description: `Using ${modelConfig.name} on ${modelConfig.device}`,
          });

          break; // Exit loop on success

        } catch (error) {
          console.error(`Failed to load ${modelConfig.name} on ${modelConfig.device}:`, error);

          // If this is the last model to try, show error
          if (modelConfig === modelsToTry[modelsToTry.length - 1]) {
            toast({
              title: "Model Loading Failed",
              description: "Unable to load any AI model. Please check your browser compatibility.",
              variant: "destructive",
            });
          }
        }
      }

      setIsModelLoading(false);
    };

    initModel();
  }, [toast]);

  useEffect(() => {
    // Auto-scroll to bottom when new messages are added
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
    }
  }, [messages]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || !generator || isLoading) return;

    // Security validation
    const validation = validateChatInput(input);
    if (!validation.isValid) {
      toast({
        title: "Invalid Input",
        description: validation.error,
        variant: "destructive",
      });
      return;
    }

    // Rate limiting check
    if (!checkRateLimit()) {
      toast({
        title: "Rate Limit Exceeded",
        description: "Please wait before sending another message.",
        variant: "destructive",
      });
      return;
    }

    const sanitizedInput = validation.sanitized || input;
    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: sanitizedInput,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setIsLoading(true);

    try {
      // Prepare conversation context
      const conversationContext = messages
        .map(msg => `${msg.role === 'user' ? 'Human' : 'Assistant'}: ${msg.content}`)
        .join('\n');
      
      const prompt = `${conversationContext}\nHuman: ${sanitizedInput}\nAssistant:`;

      const result = await generator(prompt, {
        max_new_tokens: 256,
        temperature: 0.7,
        do_sample: true,
        repetition_penalty: 1.1,
      });

      const response = result[0].generated_text.split('Assistant:').pop()?.trim() || 'I apologize, but I could not generate a response.';

      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: response,
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, assistantMessage]);
    } catch (error) {
      console.error('Error generating response:', error);
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: 'I apologize, but I encountered an error while processing your request.',
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="h-screen bg-gradient-chat flex flex-col">
      {/* Header */}
      <div className="border-b bg-card/80 backdrop-blur-sm p-4">
        <div className="flex items-center gap-3">
          <Avatar className="h-8 w-8">
            <AvatarFallback className="bg-primary text-primary-foreground">
              <Bot className="h-4 w-4" />
            </AvatarFallback>
          </Avatar>
          <div>
            <h1 className="font-semibold">Local Llama Chat</h1>
            <p className="text-sm text-muted-foreground">
              {isModelLoading ? 'Loading model...' : 'Powered by Llama 3.2 1B'}
            </p>
          </div>
        </div>
      </div>

      {/* Messages */}
      <ScrollArea className="flex-1 p-4" ref={scrollAreaRef}>
        <div className="space-y-4 max-w-4xl mx-auto">
          {messages.length === 0 && !isModelLoading && (
            <div className="text-center py-12">
              <Bot className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-medium mb-2">Welcome to Local Llama Chat</h3>
              <p className="text-muted-foreground">
                Start a conversation with your local AI assistant. The model is running entirely in your browser!
              </p>
            </div>
          )}

          {isModelLoading && (
            <div className="text-center py-12">
              <Loader2 className="h-8 w-8 mx-auto mb-4 animate-spin text-primary" />
              <h3 className="text-lg font-medium mb-2">Loading AI Model</h3>
              <p className="text-muted-foreground">
                Please wait while we initialize the Llama model...
              </p>
            </div>
          )}

          {messages.map((message) => (
            <div
              key={message.id}
              className={cn(
                "flex gap-3 animate-fade-in",
                message.role === 'user' ? 'flex-row-reverse' : 'flex-row'
              )}
            >
              <Avatar className="h-8 w-8 shrink-0">
                <AvatarFallback 
                  className={cn(
                    message.role === 'user' 
                      ? 'bg-chat-message-user text-chat-message-user-foreground' 
                      : 'bg-primary text-primary-foreground'
                  )}
                >
                  {message.role === 'user' ? (
                    <User className="h-4 w-4" />
                  ) : (
                    <Bot className="h-4 w-4" />
                  )}
                </AvatarFallback>
              </Avatar>
              
              <Card 
                className={cn(
                  "p-3 max-w-[80%] bg-gradient-message transition-smooth",
                  message.role === 'user' 
                    ? 'bg-chat-message-user text-chat-message-user-foreground' 
                    : 'bg-chat-message-ai text-chat-message-ai-foreground'
                )}
              >
                <p className="text-sm leading-relaxed whitespace-pre-wrap">
                  {message.content}
                </p>
                <p className="text-xs opacity-60 mt-2">
                  {message.timestamp.toLocaleTimeString([], { 
                    hour: '2-digit', 
                    minute: '2-digit' 
                  })}
                </p>
              </Card>
            </div>
          ))}

          {isLoading && (
            <div className="flex gap-3">
              <Avatar className="h-8 w-8 shrink-0">
                <AvatarFallback className="bg-primary text-primary-foreground">
                  <Bot className="h-4 w-4" />
                </AvatarFallback>
              </Avatar>
              
              <Card className="p-3 bg-chat-message-ai text-chat-message-ai-foreground">
                <div className="flex items-center gap-2">
                  <div className="flex gap-1">
                    <div className="w-2 h-2 bg-current rounded-full animate-typing"></div>
                    <div className="w-2 h-2 bg-current rounded-full animate-typing" style={{ animationDelay: '0.2s' }}></div>
                    <div className="w-2 h-2 bg-current rounded-full animate-typing" style={{ animationDelay: '0.4s' }}></div>
                  </div>
                  <span className="text-xs opacity-60">AI is thinking...</span>
                </div>
              </Card>
            </div>
          )}
        </div>
      </ScrollArea>

      {/* Input */}
      <div className="border-t bg-card/80 backdrop-blur-sm p-4">
        <form onSubmit={handleSubmit} className="max-w-4xl mx-auto">
          <div className="flex gap-2">
            <Input
              value={input}
              onChange={(e) => setInput(e.target.value)}
              placeholder={isModelLoading ? "Loading model..." : "Type your message..."}
              disabled={isLoading || isModelLoading || !generator}
              className="flex-1 transition-smooth focus:ring-2 focus:ring-primary/20"
            />
            <Button 
              type="submit" 
              disabled={!input.trim() || isLoading || isModelLoading || !generator}
              size="icon"
              className="shrink-0 transition-spring hover:scale-105"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};