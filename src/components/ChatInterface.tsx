import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Card } from '@/components/ui/card';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Send, Bot, User, Loader2, AlertTriangle, Settings } from 'lucide-react';
import { cn } from '@/lib/utils';
import { validateChatInput, checkRateLimit } from '@/lib/security';
import { useToast } from '@/hooks/use-toast';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

interface OllamaModel {
  name: string;
  model: string;
  size: number;
  modified_at: string;
  digest: string;
  details: {
    parameter_size: string;
    quantization_level: string;
    family: string;
  };
}

export const ChatInterface = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isModelLoading, setIsModelLoading] = useState(false);
  const [availableModels, setAvailableModels] = useState<OllamaModel[]>([]);
  const [selectedModel, setSelectedModel] = useState<string>('');
  const [isOllamaConnected, setIsOllamaConnected] = useState(false);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();

  useEffect(() => {
    // Check Ollama connection and fetch available models
    const initOllama = async () => {
      setIsModelLoading(true);

      try {
        console.log('Connecting to Ollama...');

        // Check if Ollama is running
        const response = await fetch('http://localhost:11434/api/tags');

        if (!response.ok) {
          throw new Error('Ollama server not responding');
        }

        const data = await response.json();
        const models = data.models || [];

        setAvailableModels(models);
        setIsOllamaConnected(true);

        // Auto-select the first available model
        if (models.length > 0) {
          setSelectedModel(models[0].name);
        }

        console.log('Successfully connected to Ollama');
        console.log('Available models:', models.map((m: OllamaModel) => m.name));

        toast({
          title: "Ollama Connected",
          description: `Found ${models.length} model(s) available`,
        });

      } catch (error) {
        console.error('Failed to connect to Ollama:', error);
        setIsOllamaConnected(false);

        toast({
          title: "Ollama Connection Failed",
          description: "Make sure Ollama is running on localhost:11434",
          variant: "destructive",
        });
      } finally {
        setIsModelLoading(false);
      }
    };

    initOllama();
  }, [toast]);

  useEffect(() => {
    // Auto-scroll to bottom when new messages are added
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
    }
  }, [messages]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || !isOllamaConnected || !selectedModel || isLoading) return;

    // Security validation
    const validation = validateChatInput(input);
    if (!validation.isValid) {
      toast({
        title: "Invalid Input",
        description: validation.error,
        variant: "destructive",
      });
      return;
    }

    // Rate limiting check
    if (!checkRateLimit()) {
      toast({
        title: "Rate Limit Exceeded",
        description: "Please wait before sending another message.",
        variant: "destructive",
      });
      return;
    }

    const sanitizedInput = validation.sanitized || input;
    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: sanitizedInput,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setIsLoading(true);

    try {
      // Prepare conversation context for Ollama
      const conversationMessages = [
        ...messages.map(msg => ({
          role: msg.role,
          content: msg.content
        })),
        {
          role: 'user' as const,
          content: sanitizedInput
        }
      ];

      const response = await fetch('http://localhost:11434/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: selectedModel,
          messages: conversationMessages,
          stream: false,
          options: {
            temperature: 0.7,
            top_p: 0.9,
            top_k: 40,
          }
        }),
      });

      if (!response.ok) {
        throw new Error(`Ollama API error: ${response.status}`);
      }

      const data = await response.json();
      const assistantResponse = data.message?.content || 'I apologize, but I could not generate a response.';

      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: assistantResponse,
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, assistantMessage]);
    } catch (error) {
      console.error('Error generating response:', error);
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: 'I apologize, but I encountered an error while processing your request. Please make sure Ollama is running.',
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);

      toast({
        title: "Error",
        description: "Failed to get response from Ollama. Check console for details.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="h-screen bg-gradient-chat flex flex-col">
      {/* Header */}
      <div className="border-b bg-card/80 backdrop-blur-sm p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Avatar className="h-8 w-8">
              <AvatarFallback className="bg-primary text-primary-foreground">
                <Bot className="h-4 w-4" />
              </AvatarFallback>
            </Avatar>
            <div>
              <h1 className="font-semibold">Local Ollama Chat</h1>
              <p className="text-sm text-muted-foreground">
                {isModelLoading ? 'Connecting to Ollama...' :
                 isOllamaConnected ? `Connected • ${availableModels.length} models` : 'Disconnected'}
              </p>
            </div>
          </div>

          {/* Model Selection */}
          {isOllamaConnected && availableModels.length > 0 && (
            <div className="flex items-center gap-2">
              <Settings className="h-4 w-4 text-muted-foreground" />
              <Select value={selectedModel} onValueChange={setSelectedModel}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Select model" />
                </SelectTrigger>
                <SelectContent>
                  {availableModels.map((model) => (
                    <SelectItem key={model.name} value={model.name}>
                      <div className="flex flex-col">
                        <span>{model.name}</span>
                        <span className="text-xs text-muted-foreground">
                          {model.details.parameter_size} • {(model.size / 1024 / 1024 / 1024).toFixed(1)}GB
                        </span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
        </div>
      </div>

      {/* Messages */}
      <ScrollArea className="flex-1 p-4" ref={scrollAreaRef}>
        <div className="space-y-4 max-w-4xl mx-auto">
          {messages.length === 0 && !isModelLoading && isOllamaConnected && (
            <div className="text-center py-12">
              <Bot className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-medium mb-2">Welcome to Local Ollama Chat</h3>
              <p className="text-muted-foreground">
                Start a conversation with your local AI assistant powered by {selectedModel}!
              </p>
            </div>
          )}

          {messages.length === 0 && !isModelLoading && !isOllamaConnected && (
            <div className="text-center py-12">
              <AlertTriangle className="h-12 w-12 mx-auto mb-4 text-destructive" />
              <h3 className="text-lg font-medium mb-2">Ollama Not Connected</h3>
              <p className="text-muted-foreground">
                Please make sure Ollama is running on localhost:11434
              </p>
            </div>
          )}

          {isModelLoading && (
            <div className="text-center py-12">
              <Loader2 className="h-8 w-8 mx-auto mb-4 animate-spin text-primary" />
              <h3 className="text-lg font-medium mb-2">Connecting to Ollama</h3>
              <p className="text-muted-foreground">
                Please wait while we connect to your local Ollama instance...
              </p>
            </div>
          )}

          {messages.map((message) => (
            <div
              key={message.id}
              className={cn(
                "flex gap-3 animate-fade-in",
                message.role === 'user' ? 'flex-row-reverse' : 'flex-row'
              )}
            >
              <Avatar className="h-8 w-8 shrink-0">
                <AvatarFallback 
                  className={cn(
                    message.role === 'user' 
                      ? 'bg-chat-message-user text-chat-message-user-foreground' 
                      : 'bg-primary text-primary-foreground'
                  )}
                >
                  {message.role === 'user' ? (
                    <User className="h-4 w-4" />
                  ) : (
                    <Bot className="h-4 w-4" />
                  )}
                </AvatarFallback>
              </Avatar>
              
              <Card 
                className={cn(
                  "p-3 max-w-[80%] bg-gradient-message transition-smooth",
                  message.role === 'user' 
                    ? 'bg-chat-message-user text-chat-message-user-foreground' 
                    : 'bg-chat-message-ai text-chat-message-ai-foreground'
                )}
              >
                <p className="text-sm leading-relaxed whitespace-pre-wrap">
                  {message.content}
                </p>
                <p className="text-xs opacity-60 mt-2">
                  {message.timestamp.toLocaleTimeString([], { 
                    hour: '2-digit', 
                    minute: '2-digit' 
                  })}
                </p>
              </Card>
            </div>
          ))}

          {isLoading && (
            <div className="flex gap-3">
              <Avatar className="h-8 w-8 shrink-0">
                <AvatarFallback className="bg-primary text-primary-foreground">
                  <Bot className="h-4 w-4" />
                </AvatarFallback>
              </Avatar>
              
              <Card className="p-3 bg-chat-message-ai text-chat-message-ai-foreground">
                <div className="flex items-center gap-2">
                  <div className="flex gap-1">
                    <div className="w-2 h-2 bg-current rounded-full animate-typing"></div>
                    <div className="w-2 h-2 bg-current rounded-full animate-typing" style={{ animationDelay: '0.2s' }}></div>
                    <div className="w-2 h-2 bg-current rounded-full animate-typing" style={{ animationDelay: '0.4s' }}></div>
                  </div>
                  <span className="text-xs opacity-60">AI is thinking...</span>
                </div>
              </Card>
            </div>
          )}
        </div>
      </ScrollArea>

      {/* Input */}
      <div className="border-t bg-card/80 backdrop-blur-sm p-4">
        <form onSubmit={handleSubmit} className="max-w-4xl mx-auto">
          <div className="flex gap-2">
            <Input
              value={input}
              onChange={(e) => setInput(e.target.value)}
              placeholder={
                isModelLoading ? "Connecting to Ollama..." :
                !isOllamaConnected ? "Ollama not connected..." :
                !selectedModel ? "Select a model..." :
                "Type your message..."
              }
              disabled={isLoading || isModelLoading || !isOllamaConnected || !selectedModel}
              className="flex-1 transition-smooth focus:ring-2 focus:ring-primary/20"
            />
            <Button
              type="submit"
              disabled={!input.trim() || isLoading || isModelLoading || !isOllamaConnected || !selectedModel}
              size="icon"
              className="shrink-0 transition-spring hover:scale-105"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};