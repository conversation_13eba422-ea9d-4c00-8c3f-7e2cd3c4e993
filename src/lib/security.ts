// Security utilities for input validation and sanitization

const MAX_MESSAGE_LENGTH = 2000;
const MAX_MESSAGES_PER_MINUTE = 10;

// Simple rate limiting store (in production, use Redis or similar)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

export interface SecurityValidationResult {
  isValid: boolean;
  error?: string;
  sanitized?: string;
}

/**
 * Validates and sanitizes chat input
 */
export function validateChatInput(input: string): SecurityValidationResult {
  // Check for empty input
  if (!input || !input.trim()) {
    return { isValid: false, error: 'Message cannot be empty' };
  }

  // Check length
  if (input.length > MAX_MESSAGE_LENGTH) {
    return { 
      isValid: false, 
      error: `Message too long. Maximum ${MAX_MESSAGE_LENGTH} characters allowed.` 
    };
  }

  // Basic sanitization - remove potential script tags and suspicious patterns
  const sanitized = input
    .replace(/<script[^>]*>.*?<\/\script>/gi, '') // Remove script tags
    .replace(/javascript:/gi, '') // Remove javascript: protocols
    .replace(/on\w+\s*=/gi, '') // Remove event handlers
    .replace(/data:text\/html/gi, '') // Remove data URLs
    .trim();

  // Check for suspicious patterns after sanitization
  const suspiciousPatterns = [
    /eval\s*\(/i,
    /function\s*\(/i,
    /setTimeout\s*\(/i,
    /setInterval\s*\(/i,
    /<iframe/i,
    /<object/i,
    /<embed/i,
    /vbscript:/i,
    /expression\s*\(/i
  ];

  for (const pattern of suspiciousPatterns) {
    if (pattern.test(sanitized)) {
      return { 
        isValid: false, 
        error: 'Message contains potentially harmful content' 
      };
    }
  }

  return { isValid: true, sanitized };
}

/**
 * Simple rate limiting check
 */
export function checkRateLimit(userId: string = 'anonymous'): boolean {
  const now = Date.now();
  const windowMs = 60000; // 1 minute
  
  const userLimit = rateLimitStore.get(userId);
  
  if (!userLimit || now > userLimit.resetTime) {
    // Reset or create new limit
    rateLimitStore.set(userId, { count: 1, resetTime: now + windowMs });
    return true;
  }
  
  if (userLimit.count >= MAX_MESSAGES_PER_MINUTE) {
    return false;
  }
  
  userLimit.count++;
  return true;
}

/**
 * Sanitizes configuration objects for charts
 */
export function sanitizeChartConfig(config: Record<string, any>): Record<string, any> {
  const sanitized: Record<string, any> = {};
  
  for (const [key, value] of Object.entries(config)) {
    if (typeof value === 'string') {
      // Remove potential script injections from string values
      sanitized[key] = value
        .replace(/<script[^>]*>.*?<\/\script>/gi, '')
        .replace(/javascript:/gi, '')
        .replace(/on\w+\s*=/gi, '');
    } else if (typeof value === 'object' && value !== null) {
      // Recursively sanitize nested objects
      sanitized[key] = sanitizeChartConfig(value);
    } else {
      // Keep primitives as-is
      sanitized[key] = value;
    }
  }
  
  return sanitized;
}
